/**
 * 个人信息管理服务
 */

import {
  handleNetworkError,
  handleAuthError,
  handleProfileError,
  formatErrorMessage,
  validateAvatarFile,
  validateNickname,
} from "../utils/errorHandler";

// 获取API基础URL - 在客户端使用相对路径通过Next.js代理，在服务端使用完整URL
const getApiBaseUrl = () => {
  // 在客户端环境下使用相对路径，通过Next.js rewrites代理避免CORS问题
  if (typeof window !== "undefined") {
    return ""; // 使用相对路径，通过Next.js rewrites代理
  }
  // 在服务端环境下使用完整URL
  return process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";
};

// 防止重复请求的Promise缓存
let profilePromise: Promise<ProfileResponse> | null = null;

// 个人信息管理相关的类型定义（基于后端API规范）

export interface UserProfileUpdateRequest {
  nickname?: string | null;
}

export interface ChangeEmailRequest {
  new_email: string;
  password: string;
}

export interface VerifyEmailChangeRequest {
  token: string;
}

export interface ChangePasswordRequest {
  old_password: string;
  new_password: string;
}

export interface ChangeNicknameRequest {
  nickname: string;
  reason?: string | null;
}

export interface UploadAvatarRequest {
  file: File;
}

export interface ProfileResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export interface UserProfile {
  id: number;
  username: string;
  email: string;
  nickname?: string | null;
  avatar?: string | null;
  status: string;
  email_verified: boolean;
  role: {
    id: number;
    name: string;
    display_name?: string;
    description?: string;
    permissions?: string[];
  };
  points: number;
  title: string;
  last_login_at?: string | null;
  created_at: string;
  updated_at?: string;
  last_nickname_change?: string | null;
  nickname_change_count?: number;
  can_change_nickname?: boolean;
  next_nickname_change_date?: string | null;
}

export interface PointsHistoryItem {
  id: number;
  points: number;
  transaction_type: string;
  description: string;
  created_at: string;
}

export interface PointsHistoryResponse {
  success: boolean;
  data: {
    items: PointsHistoryItem[];
    total: number;
    page: number;
    size: number;
    total_pages: number;
  };
  message?: string;
  error?: string;
}

export interface HelpRequest {
  id: number;
  title: string;
  description: string;
  resource_type: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface HelpRequestsResponse {
  success: boolean;
  data: {
    items: HelpRequest[];
    total: number;
    page: number;
    size: number;
    total_pages: number;
  };
  message?: string;
  error?: string;
}

export interface HelpAnswer {
  id: number;
  content: string;
  is_accepted: boolean;
  created_at: string;
  request: {
    id: number;
    title: string;
  };
}

export interface HelpAnswersResponse {
  success: boolean;
  data: {
    items: HelpAnswer[];
    total: number;
    page: number;
    size: number;
    total_pages: number;
  };
  message?: string;
  error?: string;
}

export interface ProfileStatistics {
  total_help_requests: number;
  total_help_answers: number;
  accepted_answers: number;
  total_points: number;
  current_title: string;
}

export interface ActivitySummary {
  recent_help_requests: HelpRequest[];
  recent_help_answers: HelpAnswer[];
  points_this_month: number;
  rank_this_month: number;
}

/**
 * 获取个人信息
 * 带有请求去重机制，防止同时发起多个相同请求
 */
export async function getMyProfile(): Promise<ProfileResponse> {
  // 如果已经有正在进行的请求，直接返回该Promise
  if (profilePromise) {
    console.log("📋 getMyProfile: 等待正在进行的请求...");
    return profilePromise;
  }

  // 创建新的请求Promise
  profilePromise = (async (): Promise<ProfileResponse> => {
    try {
      const token = localStorage.getItem("auth_token");
      if (!token) {
        return {
          success: false,
          message: "未登录，请先登录",
        };
      }

      console.log("📋 getMyProfile: 发送新的请求获取个人信息");
      const response = await fetch(`${getApiBaseUrl()}/api/profile/me`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorResponse = handleProfileError(response.status, errorData);
        return {
          success: false,
          message: formatErrorMessage(errorResponse),
          error: errorResponse.error,
        };
      }

      const responseData = await response.json();
      console.log("📋 getMyProfile: 原始响应数据:", responseData);

      // 根据后端API规范，提取data字段
      const userData = responseData.data || responseData;
      console.log("📋 getMyProfile: 提取的用户数据:", userData);

      return {
        success: true,
        message: responseData.message || "获取个人信息成功",
        data: userData,
      };
    } catch (error) {
      console.error("📋 getMyProfile: 获取个人信息失败:", error);
      const errorResponse = handleNetworkError(error as Error);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    } finally {
      // 请求完成后清除Promise缓存，允许后续新的请求
      profilePromise = null;
    }
  })();

  return profilePromise;
}

/**
 * 更新个人信息
 */
export async function updateMyProfile(
  profileData: UserProfileUpdateRequest
): Promise<ProfileResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录，请先登录",
      };
    }

    const response = await fetch(`${getApiBaseUrl()}/api/profile/me`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(profileData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleProfileError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: "更新个人信息成功",
      data: data,
    };
  } catch (error) {
    console.error("更新个人信息失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 修改邮箱
 */
export async function changeEmail(
  emailData: ChangeEmailRequest
): Promise<ProfileResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录，请先登录",
      };
    }

    const response = await fetch(
      `${getApiBaseUrl()}/api/profile/change-email`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(emailData),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleProfileError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: data.message || "邮箱修改请求已发送，请查收验证邮件",
      data: data,
    };
  } catch (error) {
    console.error("修改邮箱失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 验证邮箱修改
 */
export async function verifyEmailChange(
  verifyData: VerifyEmailChangeRequest
): Promise<ProfileResponse> {
  try {
    const response = await fetch(
      `${getApiBaseUrl()}/api/profile/verify-email-change`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(verifyData),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleProfileError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: data.message || "邮箱修改成功",
      data: data,
    };
  } catch (error) {
    console.error("验证邮箱修改失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 上传头像
 */
export async function uploadAvatar(
  avatarData: UploadAvatarRequest
): Promise<ProfileResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录，请先登录",
      };
    }

    // 验证头像文件
    const validation = validateAvatarFile(avatarData.file);
    if (!validation.valid) {
      return {
        success: false,
        message: validation.error || "头像文件验证失败",
      };
    }

    const formData = new FormData();
    formData.append("file", avatarData.file);

    const response = await fetch(
      `${getApiBaseUrl()}/api/profile/upload-avatar`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleProfileError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: data.message || "头像上传成功",
      data: data,
    };
  } catch (error) {
    console.error("上传头像失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 修改密码
 */
export async function changePassword(
  passwordData: ChangePasswordRequest
): Promise<ProfileResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录，请先登录",
      };
    }

    const response = await fetch(
      `${getApiBaseUrl()}/api/profile/change-password`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(passwordData),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleProfileError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: data.message || "密码修改成功",
      data: data,
    };
  } catch (error) {
    console.error("修改密码失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 修改昵称
 */
export async function changeNickname(
  nicknameData: ChangeNicknameRequest
): Promise<ProfileResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录，请先登录",
      };
    }

    // 验证昵称
    const validation = validateNickname(nicknameData.nickname);
    if (!validation.valid) {
      return {
        success: false,
        message: validation.error || "昵称验证失败",
      };
    }

    const response = await fetch(
      `${getApiBaseUrl()}/api/profile/change-nickname`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(nicknameData),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleProfileError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: data.message || "昵称修改成功",
      data: data,
    };
  } catch (error) {
    console.error("修改昵称失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 获取积分历史
 */
export async function getPointsHistory(
  page: number = 1,
  size: number = 20
): Promise<PointsHistoryResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        data: {
          items: [],
          total: 0,
          page: 1,
          size: 20,
          total_pages: 0,
        },
        message: "未登录，请先登录",
      };
    }

    const response = await fetch(
      `${getApiBaseUrl()}/api/profile/points-history?page=${page}&size=${size}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleAuthError(response.status, errorData);
      return {
        success: false,
        data: {
          items: [],
          total: 0,
          page: 1,
          size: 20,
          total_pages: 0,
        },
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    // 确保返回的数据结构正确
    const responseData = data.data || data;

    return {
      success: true,
      data: {
        items: Array.isArray(responseData.items) ? responseData.items : [],
        total: responseData.total || 0,
        page: responseData.page || page,
        size: responseData.size || size,
        total_pages: responseData.total_pages || 1,
      },
      message: "获取积分历史成功",
    };
  } catch (error) {
    console.error("获取积分历史失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      data: {
        items: [],
        total: 0,
        page: 1,
        size: 20,
        total_pages: 0,
      },
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 获取我的求助列表
 */
export async function getMyHelpRequests(
  page: number = 1,
  size: number = 20,
  statusFilter?: string
): Promise<HelpRequestsResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        data: {
          items: [],
          total: 0,
          page: 1,
          size: 20,
          total_pages: 0,
        },
        message: "未登录，请先登录",
      };
    }

    let url = `${getApiBaseUrl()}/api/profile/help-requests?page=${page}&size=${size}`;
    if (statusFilter) {
      url += `&status_filter=${statusFilter}`;
    }

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleAuthError(response.status, errorData);
      return {
        success: false,
        data: {
          items: [],
          total: 0,
          page: 1,
          size: 20,
          total_pages: 0,
        },
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    // 确保返回的数据结构正确
    const responseData = data.data || data;

    return {
      success: true,
      data: {
        items: Array.isArray(responseData.items) ? responseData.items : [],
        total: responseData.total || 0,
        page: responseData.page || page,
        size: responseData.size || size,
        total_pages: responseData.total_pages || 1,
      },
      message: "获取求助列表成功",
    };
  } catch (error) {
    console.error("获取求助列表失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      data: {
        items: [],
        total: 0,
        page: 1,
        size: 20,
        total_pages: 0,
      },
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 获取我的回答列表
 */
export async function getMyHelpAnswers(
  page: number = 1,
  size: number = 20
): Promise<HelpAnswersResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        data: {
          items: [],
          total: 0,
          page: 1,
          size: 20,
          total_pages: 0,
        },
        message: "未登录，请先登录",
      };
    }

    const response = await fetch(
      `${getApiBaseUrl()}/api/profile/help-answers?page=${page}&size=${size}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleAuthError(response.status, errorData);
      return {
        success: false,
        data: {
          items: [],
          total: 0,
          page: 1,
          size: 20,
          total_pages: 0,
        },
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    // 确保返回的数据结构正确
    const responseData = data.data || data;

    return {
      success: true,
      data: {
        items: Array.isArray(responseData.items) ? responseData.items : [],
        total: responseData.total || 0,
        page: responseData.page || page,
        size: responseData.size || size,
        total_pages: responseData.total_pages || 1,
      },
      message: "获取回答列表成功",
    };
  } catch (error) {
    console.error("获取回答列表失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      data: {
        items: [],
        total: 0,
        page: 1,
        size: 20,
        total_pages: 0,
      },
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 获取个人统计信息
 */
export async function getProfileStatistics(): Promise<ProfileResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录，请先登录",
      };
    }

    const response = await fetch(`${getApiBaseUrl()}/api/profile/statistics`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleAuthError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: "获取统计信息成功",
      data: data.data || data,
    };
  } catch (error) {
    console.error("获取统计信息失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}

/**
 * 获取活动摘要
 */
export async function getActivitySummary(): Promise<ProfileResponse> {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      return {
        success: false,
        message: "未登录，请先登录",
      };
    }

    const response = await fetch(
      `${getApiBaseUrl()}/api/profile/activity-summary`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorResponse = handleAuthError(response.status, errorData);
      return {
        success: false,
        message: formatErrorMessage(errorResponse),
        error: errorResponse.error,
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: "获取活动摘要成功",
      data: data.data || data,
    };
  } catch (error) {
    console.error("获取活动摘要失败:", error);
    const errorResponse = handleNetworkError(error as Error);
    return {
      success: false,
      message: formatErrorMessage(errorResponse),
      error: errorResponse.error,
    };
  }
}
